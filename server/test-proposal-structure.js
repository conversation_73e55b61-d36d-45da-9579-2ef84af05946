import * as multisig from '@sqds/multisig'
import { Connection, PublicKey } from '@solana/web3.js'
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SOLANA_RPC_URL } from './constants.js'

const connection = new Connection(SOLANA_RPC_URL)

async function testProposalStructure() {
  try {
    console.log('Testing Proposal data structure...')
    
    // 获取多签账户信息
    const { transactionIndex } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)
    console.log('Latest transaction index:', Number(transactionIndex))
    
    // 测试最近的几个提案
    for (let i = Math.max(1, Number(transactionIndex) - 2); i <= Number(transactionIndex); i++) {
      console.log(`\n--- Testing Proposal #${i} ---`)
      
      const [proposalPda] = multisig.getProposalPda({ 
        multisigPda: MULTISIG_ADDRESS_PUBKEY, 
        transactionIndex: i, 
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY 
      })
      
      const proposalAccount = await connection.getAccountInfo(proposalPda)
      
      if (!proposalAccount) {
        console.log(`Proposal #${i}: No proposal data`)
        continue
      }
      
      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]
      console.log(`Proposal #${i} fields:`, Object.keys(proposalData))
      
      // 检查是否有 threshold 字段
      if (proposalData.threshold !== undefined) {
        console.log(`Proposal #${i} threshold:`, proposalData.threshold)
      } else {
        console.log(`Proposal #${i}: No threshold field found`)
      }
      
      // 显示其他重要字段
      console.log(`Proposal #${i} approved:`, proposalData.approved.length)
      console.log(`Proposal #${i} status:`, proposalData.status.__kind)
    }
    
  } catch (error) {
    console.error('Error testing proposal structure:', error.message)
  }
}

testProposalStructure()

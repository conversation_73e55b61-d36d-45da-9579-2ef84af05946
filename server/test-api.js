import { getTransactions } from './service.js'

// 模拟 Koa context
const mockCtx = {
  request: {
    body: {
      page: 1,
      pageSize: 3
    }
  },
  body: null
}

async function testTransactionsAPI() {
  try {
    console.log('Testing /api/transactions with author and threshold fixes...')
    
    await getTransactions(mockCtx)
    
    if (mockCtx.body && mockCtx.body.transactions) {
      console.log(`Found ${mockCtx.body.transactions.length} transactions`)
      
      mockCtx.body.transactions.forEach((tx, index) => {
        console.log(`\n--- Transaction #${tx.transactionIndex} ---`)
        console.log(`Author: ${tx.author || 'N/A'}`)
        console.log(`Threshold: ${tx.threshold}`)
        console.log(`Approvals: ${tx.approvals}`)
        console.log(`Status: ${tx.status}`)
        console.log(`Amount: ${tx.amount} ${tx.chain || 'N/A'}`)
        console.log(`To: ${tx.to || 'N/A'}`)
      })
    } else {
      console.log('No transactions returned')
    }
    
  } catch (error) {
    console.error('Error testing transactions API:', error.message)
  }
}

testTransactionsAPI()
